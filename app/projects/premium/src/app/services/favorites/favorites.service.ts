import { Injectable } from '@angular/core';
import { distinctJSON } from '@pb/ui';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { distinctUntilChanged, map, switchMap, tap } from 'rxjs/operators';

import { ApiService } from '../api.service';
import { IPreview } from './../../models/preview';
import {
  GET_FAV_GIRLS,
  GET_FAV_GIRLS_META,
  IFavGirlResult,
  MapFavGirlResultToPreviews,
} from './definitions/queries/girl';
import {
  GET_FAV_GIRL_INFOS,
  GET_FAV_GIRL_INFOS_META,
  IFavGirlInfoResult,
  MapFavGirlInfoResultToPreviews,
} from './definitions/queries/girl-infos';
import {
  GET_FAV_IMAGES,
  GET_FAV_IMAGES_META,
  IFavImageResult,
  MapFavImageResultToPreviews,
} from './definitions/queries/images';
import {
  GET_FAV_VIDEOS,
  GET_FAV_VIDEOS_META,
  IFavVideoResult,
  MapFavVideoResultToPreviews,
} from './definitions/queries/videos';

interface Favorite {
  id: string;
  entity_type: FavoriteEntityType;
  media_id?: string;
  nexx_id?: string;
  girl_id?: string;
  girl_info_id?: string;
  previewImage?: string;
  previewImageLowRes?: string;
  focalPoint?: { x: number; y: number };
}

export type FavoriteEntityType = 'girl' | 'media' | 'girl_info';
export type FavoriteFlagId =
  | 'video_flag'
  | 'image_flag'
  | 'girl_info_flag'
  | 'girl_flag';
export type FavoriteType = 'girl' | 'image' | 'video' | 'girl-infos';

export type FavoriteFlag = {
  flag_id: FavoriteFlagId;
  entity_type: FavoriteEntityType;
  entity_id: string;
};

export type FavoriteValueFlag = { id: string } & (
  | { girl_id: string; entity_type: 'girl' }
  | { media_id: string; entity_type: 'girl_info' | 'media' }
);

const APIEndpoints: { [key in FavoriteType]: string } = {
  girl: 'girls',
  video: 'videos',
  image: 'images',
  'girl-infos': 'girl-infos',
};

// TODO: Rebuild to use Subjects for data storing and expose the observables for frontend usage.
@Injectable({
  providedIn: 'root',
})
export class FavoritesService {
  private girlIDsBehav = new BehaviorSubject<string[]>([]);
  private imageIDsBehav = new BehaviorSubject<string[]>([]);
  private videoIDsBehav = new BehaviorSubject<string[]>([]);
  private girlInfoIDsBehav = new BehaviorSubject<string[]>([]);

  public favoriteIds: Observable<{ [key in FavoriteType]: string[] }> =
    combineLatest([
      this.girlIDsBehav,
      this.imageIDsBehav,
      this.girlInfoIDsBehav,
      this.videoIDsBehav,
    ]).pipe(
      map(([girl, image, girlInfos, video]) => ({
        girl,
        image,
        video,
        'girl-infos': girlInfos,
      })),
      distinctJSON(),
    );

  getIdsForType(type: FavoriteType): Observable<string[]> {
    switch (type) {
      case 'girl-infos':
        return this.girlInfoIDsBehav.pipe(
          distinctUntilChanged((a, b) => a === b),
        );
      default:
        return this[`${type}IDsBehav`].pipe(
          distinctUntilChanged((a, b) => a === b),
        );
    }
  }

  getIsFavorite(id: string | number, type: FavoriteType): Observable<boolean> {
    return this.getIdsForType(type).pipe(
      map((v) => v.indexOf('' + id) >= 0),
      distinctUntilChanged((a, b) => a === b),
    );
  }

  private getIDBehavOfType(type: FavoriteType) {
    switch (type) {
      case 'girl':
        return this.girlIDsBehav;
      case 'girl-infos':
        return this.girlInfoIDsBehav;
      case 'image':
        return this.imageIDsBehav;
      case 'video':
        return this.videoIDsBehav;
    }
  }

  constructor(
    private apiService: ApiService,
    private apollo: Apollo,
  ) {
    this.refreshAllFavorites();
  }

  public refreshAllFavorites() {
    this.refreshFavorite('girl');
    this.refreshFavorite('image');
    this.refreshFavorite('girl-infos');
    this.refreshFavorite('video');
  }

  private async refreshFavorite(type: FavoriteType) {
    const flags = await this.apiService
      .get<FavoriteValueFlag[]>('favorite/' + APIEndpoints[type])
      .toPromise();

    const idsBehav = this.getIDBehavOfType(type);

    idsBehav.next(
      flags.map((flag) => {
        switch (flag.entity_type) {
          case 'girl':
            return flag.girl_id;
          case 'media':
          case 'girl_info':
            return flag.media_id;
          // default:
          //   return flag.id;
        }
      }),
    );
  }

  public async toggleFavorite(type: FavoriteType, entityId: string) {
    let requestBody: Partial<{
      flag_id: FavoriteFlagId;
      entity_type: FavoriteEntityType;
      entity_id: string;
    }> = {
      entity_id: entityId,
    };

    switch (type) {
      case 'girl':
        requestBody.entity_type = 'girl';
        requestBody.flag_id = 'girl_flag';
        break;
      case 'image':
        requestBody.entity_type = 'media';
        requestBody.flag_id = 'image_flag';
        break;
      case 'girl-infos':
        requestBody.entity_type = 'girl_info';
        requestBody.flag_id = 'girl_info_flag';
        break;
      case 'video':
        requestBody.entity_type = 'media';
        requestBody.flag_id = 'video_flag';
        break;
    }

    await this.apiService.post('flag/create', requestBody).toPromise();
    this.toggleItem(type, entityId);
  }

  // I know its unreadable
  private toggleItem(type: FavoriteType, item: string) {
    const behav = this.getIDBehavOfType(type);
    let data = behav.getValue();
    if (data.includes(item)) {
      behav.next(data.filter((el) => el !== item));
    } else {
      behav.next([...data, item]);
    }
    this.refreshFavorite(type);
  }

  public getPreviews(
    type: FavoriteType,
    { pageSize = 8, page = 0 }: { pageSize?: number; page?: number } = {},
  ): Observable<IPreview[]> {
    const variables = { pageSize, page };
    switch (type) {
      case 'girl-infos':
        return this.apollo
          .query<{ pbFavoritesGirlInfos: { results: IFavGirlInfoResult[] } }>({
            query: GET_FAV_GIRL_INFOS,
            variables,
            fetchPolicy: 'no-cache',
          })
          .pipe(
            map((v) =>
              MapFavGirlInfoResultToPreviews(v.data.pbFavoritesGirlInfos),
            ),
          );
      case 'image':
        return this.apollo
          .query<{ pbFavoritesImages: { results: IFavImageResult[] } }>({
            query: GET_FAV_IMAGES,
            variables,
            fetchPolicy: 'no-cache',
          })
          .pipe(
            map((v) => MapFavImageResultToPreviews(v.data.pbFavoritesImages)),
          );
      case 'girl':
        return this.apollo
          .query<{ pbFavoritesGirls: { results: IFavGirlResult[] } }>({
            query: GET_FAV_GIRLS,
            variables,
            fetchPolicy: 'no-cache',
          })
          .pipe(
            map((v) => MapFavGirlResultToPreviews(v.data.pbFavoritesGirls)),
          );
      case 'video':
        return this.apollo
          .query<{ pbFavoritesVideos: { results: IFavVideoResult[] } }>({
            query: GET_FAV_VIDEOS,
            variables,
            fetchPolicy: 'no-cache',
          })
          .pipe(
            map((v) => MapFavVideoResultToPreviews(v.data.pbFavoritesVideos)),
          tap(v => console.log('videos', v))
          );
    }
  }

  public getPreviewsDynamic(
    $data: Observable<[FavoriteType, { pageSize?: number; page?: number }]>,
  ): Observable<IPreview[]> {
    return $data.pipe(switchMap((params) => this.getPreviews(...params)));
  }

  public getCount(type: FavoriteType): Observable<number> {
    switch (type) {
      case 'image':
        return this.apollo
          .query<{ pbFavoritesImages: { count: number } }>({
            query: GET_FAV_IMAGES_META,
            fetchPolicy: 'no-cache',
          })
          .pipe(map((v) => v.data.pbFavoritesImages.count));
      case 'girl-infos':
        return this.apollo
          .query<{ pbFavoritesGirlInfos: { count: number } }>({
            query: GET_FAV_GIRL_INFOS_META,
            fetchPolicy: 'no-cache',
          })
          .pipe(map((v) => v.data.pbFavoritesGirlInfos.count));
      case 'girl':
        return this.apollo
          .query<{ pbFavoritesGirls: { count: number } }>({
            query: GET_FAV_GIRLS_META,
            fetchPolicy: 'no-cache',
          })
          .pipe(map((v) => v.data.pbFavoritesGirls.count));
      default:
        return this.apollo
          .query<{ pbFavoritesVideos: { count: number } }>({
            query: GET_FAV_VIDEOS_META,
            fetchPolicy: 'no-cache',
          })
          .pipe(map((v) => v.data.pbFavoritesVideos.count));
    }
  }

  public getCountDynamic($type: Observable<FavoriteType>): Observable<number> {
    return $type.pipe(switchMap((v) => this.getCount(v)));
  }
}
