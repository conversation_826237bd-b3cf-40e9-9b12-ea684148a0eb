@import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");
@import url("https://use.typekit.net/hpr8uwl.css");
@import "lightgallery/css/lightgallery-bundle.min.css";
@import "@angular/cdk/overlay-prebuilt.css";
@import "assets/fonts/inter/inter.css";

@font-face {
  font-family: "Bauer Bodoni";
  src:
    url("assets/fonts/bb8422a9-7303-4111-8be4-7de2f583aaf3.woff2")
      format("woff2"),
    url("assets/fonts/6ab1eb08-75f2-4c1a-9911-2752e3fd6ec9.woff") format("woff");
}

@font-face {
  font-family: "Stag Sans Web";
  src:
    url("assets/fonts/StagSans-Medium-Web.woff2") format("woff2"),
    url("assets/fonts/StagSans-Medium-Web.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-stretch: normal;
}

@font-face {
  font-family: "Stag Sans Web";
  src:
    url("assets/fonts/StagSans-Book-Web.woff2") format("woff2"),
    url("assets/fonts/StagSans-Book-Web.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-stretch: normal;
}

/*@font-face {*/
/*  font-family: "Georgia";*/
/*  src: url("assets/fonts/Georgia.woff") format("woff2");*/
/*  font-weight: 400;*/
/*  font-style: normal;*/
/*  font-stretch: normal;*/
/*}*/

/* @import '~swiper/swiper-bundle'; */

@tailwind base;

@tailwind components;

:root {
  color-scheme: dark; /* dark scrollbar */
}

html,
body {
  @apply w-full min-h-full font-inter;
}

body {
  @apply flex flex-col;
}

* {
  outline-color: white;
}

label {
  letter-spacing: 2.45px;
  @apply text-sm mt-4 mb-2 font-medium font-inter uppercase;
}

label:not(.no-suffix)::after {
  content: ":";
  @apply text-xs;
}

select:not(.plain),
input[type="number"]:not(.plain),
input[type="date"]:not(.plain),
input[type="password"]:not(.plain),
input[type="text"]:not(.plain) {
  @apply outline-none border border-gray-500 p-2 font-inter h-12;
}

::placeholder {
  @apply text-gray-500 font-inter font-light !important;
}

input:focus {
  @apply border-golden;
}

input[type="submit"] {
  @apply bg-golden px-4 py-2 text-white font-bold font-inter text-sm uppercase shadow;
}

input[type="submit"]:disabled {
  @apply opacity-50;
}

input[type="checkbox"]:not(.plain),
input[type="radio"]:not(.plain) {
  @apply flex justify-center items-center w-6 h-6;
}

input[type="checkbox"]:not(.plain):before,
input[type="radio"]:not(.plain):before {
  @apply flex flex-1 w-full h-full;
  content: "";
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  background-color: white;
  background-image: url("assets/input/oval-base.svg");
}

input[type="checkbox"]:not(.plain):checked:before {
  background-image: url("assets/input/oval-checkbox-selected.svg");
}

input[type="radio"]:not(.plain):checked:before {
  background-image: url("assets/input/oval-radio-selected.svg");
}

ul li {
  @apply my-1;
}

/* Headlines */

h1, .h1,
h2, .h2 {
  @apply font-bauer;
}

h3, .h3,
h5, .h5,
h6, .h6 {
  letter-spacing: 2.45px;
}

h2, .h2 {
  @apply font-bold;
  line-height: 2.625rem !important;
  line-height: 42px;

  @screen md {
    line-height: 3.25rem !important;
    line-height: 52px;
  }
}

h1:not(.h2), .h1 {
  @apply text-3xl inline-flex flex-col items-start justify-end;
}

h1:not(.h2)::after, .h1::after {
  content: "";
  @apply inline-flex border-b border-golden w-20;
}

h2, .h2 {
  @apply text-4xxl;
}

h3, .h3 {
  @apply font-bauer text-xl;
  letter-spacing: -0.43px;
}

@layer base {
  .headline-4 {
    font-size: 12px;
    letter-spacing: 0.16em;
    @apply font-inter font-normal uppercase;

    @media (min-width: 1024px) {
      font-size: 14px;
    }
  }

  h4 {
    @apply headline-4;
  }
}

h5, .h5 {
  @apply font-inter text-lg leading-none text-gray-400;
}

h6, .h6 {
  @apply text-sm;
}

@screen md {
  h1:not(.h2), .h1 {
    @apply text-6xl;
  }

  h2, .h2 {
    @apply text-5xl;
  }

  h3, .h3 {
    @apply text-4xl;
  }
}

@responsive {
  .container {
    @apply mx-auto p-4;
  }
}

hr.chip {
  @apply border-golden w-32 border-b-2;
}

carousel {
  @apply relative z-10 !important;
}

.close-icon {
  @apply relative inline-flex border border-transparent;
}

.close-icon::before,
.close-icon::after {
  content: "";
  @apply block absolute origin-center transform rotate-45;
  border-color: initial !important;
}

.close-icon::before {
  @apply border-b w-full top-1/2 -translate-y-1/2 left-0;
  border-bottom-width: inherit;
}

.close-icon::after {
  @apply border-l h-full left-1/2 -translate-x-1/2 top-0;
  border-left-width: inherit;
}

html,
body {
  @apply text-white;
  --swiper-pagination-bullet-inactive-color: rgba(
    197,
    163,
    80,
    var(--tw-border-opacity)
  );
  --swiper-pagination-color: #c5a350;
  --swiper-pagination-bullet-width: 0.5rem;
  --swiper-pagination-bullet-height: 0.5rem;
  --swiper-pagination-bullet-border-radius: 100%;
}

html {
  @apply bg-black;
}

body {
  @apply bg-gray-900 min-h-screen;
}

app-root {
  @apply opacity-0 transition-opacity duration-300;
}

@tailwind utilities;

.play {
  background-color: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
}

swiper .swiper-pagination-bullet {
  @apply bg-golden rounded-none w-8 h-1 mr-2;
}

swiper .swiper-pagination {
  @apply h-10 leading-10 bottom-0;
}

.ratio-video {
  aspect-ratio: 16/9;
}

/* Gallery Styles */
.gallery-item {
  margin: 5px;
}

.lg-content {
  height: calc(100% - 115px);
}

.lg-thumb.lg-group {
  display: flex;
  margin-bottom: 5px;
}

.lg-thumb-item {
  width: 100px;
  height: 100px;
  opacity: 0.4;
  margin: 0 5px;
  border: none;
}

.lg-thumb-item img {
  object-fit: cover;
  display: block;
  width: 100%;
  height: 100%;
}

.lg-components-open .lg-thumb-outer.lg-thumb-align-middle.lg-grab {
  position: absolute;
  bottom: 0;
}

.lg-outer .lg-thumb-item {
  border-color: transparent;
}

.lg-outer .lg-thumb-item:hover {
  border-color: #fff;
}

.caption {
  display: none;
}

.lg-outer .lg-thumb-item.active {
  opacity: 1;
  border: 2px solid #fff;
}

.lg-progress-bar .lg-progress {
  background-color: #c5a350;
}

.lg-zoom-in {
  margin: 0 50px;
}

.lg-closeBtn {
  position: fixed;
  right: 15px;
  top: 6px;
  width: 35px;
  height: 35px;
  z-index: 99999;
}

.lg-closeBtn img {
  width: 100%;
  height: 100%;
  display: block;
}

.lg-favBtn {
  width: 40px;
  height: 40px;
  position: fixed;
  z-index: 99999;
  right: 0;
  top: 8px;
  left: 70px;
}

.lg-toolbar {
  background-color: rgba(0, 0, 0, 0.45);
}

.lg-container .lg-sub-html {
  font-family: "adobe-garamond-pro";
  font-size: 18px;
  background: rgba(0, 0, 0, 0.45);
  pointer-events: none;
}

.lg-prev-slide .lg-sub-html {
  background-color: transparent;
}

.lg-actual-size.lg-icon {
  position: fixed;
  right: 100px;
}

.lg-star {
  position: fixed;
  left: 80px;
  top: 8px;
  width: 35px;
  height: 35px;
  z-index: 99999;
  cursor: pointer;
}

@media only screen and (max-width: 767px) {
  .lg-star {
    left: 9px;
    top: 55px;
  }
}

.aspect-auto {
  aspect-ratio: auto;
}

.aspect-2\/1 {
  aspect-ratio: 2 / 1;
}

@media (min-width: 640px) {
  .sm\:aspect-2\/1 {
    aspect-ratio: 2 / 1;
  }
}

.last\:mb-24:last-child {
  @apply mb-24;
}

.cdk-overlay-container .cdk-overlay-backdrop-showing {
  background: black;
  opacity: 0.7;
}

.subscription-popup.cdk-overlay-pane {
  width: 100%;
  height: 100%;

  @media (min-width: 640px) {
    height: auto;
    width: 90%;
  }

  @media (min-width: 1280px) {
    height: auto;
    width: 80%;
    max-width: 1024px;
  }
}

@media (min-width: 768px) {
  .md\:\!hidden {
    display: none !important;
  }
}

/* overwrite credit font for light gallery */
.lg-container .lg-sub-html {
  font-family: "Inter", sans-serif;
}

/* Fix video controls overlap with lightgallery controls */
.lg-outer {
  z-index: 10000;
}

.lg-outer .lg-toolbar {
  z-index: 99999;
}

.lg-outer .lg-actions {
  z-index: 99999;
}

.lg-outer .lg-counter {
  z-index: 99999;
}

/* Ensure video iframe doesn't interfere with gallery controls */
.lg-outer .lg-video-cont iframe {
  position: relative;
  z-index: 1;
}

/* Ensure all lightgallery UI elements stay above video content */
.lg-outer .lg-toolbar .lg-icon {
  z-index: 99999;
  position: relative;
}

.lg-outer .lg-prev,
.lg-outer .lg-next {
  z-index: 99999;
}

/* Specific handling for nexx.cloud video embeds */
.lg-outer .lg-video-cont {
  position: relative;
  z-index: 1;
}

.lg-outer .lg-video-cont iframe[src*="nexx.cloud"] {
  position: relative;
  z-index: 1;
}

/* Ensure lightgallery controls are always visible above video */
.lg-outer .lg-toolbar,
.lg-outer .lg-actions,
.lg-outer .lg-counter,
.lg-closeBtn,
.lg-star {
  position: relative;
  z-index: 99999 !important;
}

.cdk-dialog-container {
  outline: none !important;
}
